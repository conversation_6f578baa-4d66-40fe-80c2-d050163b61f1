"""
Data Management Module for User Profiles

This module provides thread-safe operations for managing user profile data stored in JSON format.
It serves as the primary data persistence layer for the application, handling user authentication,
profile management, and user preference storage.

Key Features:
- Thread-safe file operations using locks to prevent data corruption
- Configurable file paths via environment variables for testing flexibility
- Comprehensive error handling and logging
- Atomic operations to ensure data integrity
- Support for profile validation and backup mechanisms

Data Structure:
Each user profile contains the following fields:
- user_id (int): Unique identifier for the user
- username (str): Unique username for authentication
- password_hash (str): Hashed password for security
- name (str): User's display name
- email (str): User's email address
- phone_number (str): Contact phone number
- age (int): User's age
- gender (str): User's gender identity
- academic_level (str): Educational level (e.g., "Kindergarten", "High School")
- preferred_subject (str): Subject of interest
- learning_style (str): Learning preference (e.g., "Visual", "Auditory")
- location (str): Geographic location
- language (str): Preferred language
- timezone (str): User's timezone
- cultural_background (str): Cultural context
- hobbies (list): List of user's hobbies
- goals (list): List of user's learning goals
- strengths (list): List of user's strengths
- weaknesses (list): List of areas for improvement
- is_admin (bool): Administrative privileges flag
- histories (list): List of interaction history records
- xp (int): Experience points for gamification
- level (int): User level based on experience
- streak (int): Consecutive activity streak
- badges (list): Earned achievement badges
- answer_type_weights (dict): Personalized answer type preferences

Author: Capstone Project Team
Version: 2.0
Last Updated: 2025-06-15
"""

import json
import logging
import threading
import os
import tempfile
import shutil
from typing import Dict, List, Optional, Any, Union
from datetime import datetime

# Thread lock to prevent concurrent file access and ensure data integrity
_file_lock = threading.Lock()

# Default file path that can be overridden via environment variable for flexibility
PROFILES_FILE = os.environ.get('PROFILES_FILE_PATH', 'profiles.json')

# Profile validation schema - defines required and optional fields
REQUIRED_PROFILE_FIELDS = {
    'user_id', 'username', 'password_hash', 'name', 'email',
    'is_admin', 'histories'
}

OPTIONAL_PROFILE_FIELDS = {
    'phone_number', 'age', 'gender', 'academic_level', 'preferred_subject',
    'learning_style', 'location', 'language', 'timezone', 'cultural_background',
    'hobbies', 'goals', 'strengths', 'weaknesses', 'xp', 'level', 'streak',
    'badges', 'answer_type_weights', 'weight_adjustment_count'
}


def _validate_profile_structure(profile: Dict[str, Any]) -> bool:
    """
    Validate that a profile contains all required fields and has proper structure.

    Args:
        profile (Dict[str, Any]): Profile dictionary to validate

    Returns:
        bool: True if profile is valid, False otherwise
    """
    if not isinstance(profile, dict):
        return False

    # Check for required fields
    for field in REQUIRED_PROFILE_FIELDS:
        if field not in profile:
            logging.warning(f"Profile missing required field: {field}")
            return False

    # Validate specific field types
    if not isinstance(profile.get('histories'), list):
        logging.warning("Profile 'histories' field must be a list")
        return False

    if not isinstance(profile.get('is_admin'), bool):
        logging.warning("Profile 'is_admin' field must be a boolean")
        return False

    return True


def _create_backup(file_path: str) -> Optional[str]:
    """
    Create a backup of the profiles file before making changes.

    Args:
        file_path (str): Path to the file to backup

    Returns:
        Optional[str]: Path to backup file if successful, None otherwise
    """
    try:
        if os.path.exists(file_path):
            backup_path = f"{file_path}.backup.{datetime.now().strftime('%Y%m%d_%H%M%S')}"
            shutil.copy2(file_path, backup_path)
            logging.info(f"Backup created: {backup_path}")
            return backup_path
    except Exception as e:
        logging.error(f"Failed to create backup: {e}")
    return None


def load_profiles(file_path: Optional[str] = None) -> Dict[str, Dict[str, Any]]:
    """
    Load user profiles from the JSON profiles file with comprehensive error handling.

    This function reads the profiles file, validates the data structure, and returns
    a dictionary of profiles keyed by username. It includes automatic data migration
    and validation to ensure data integrity.

    Args:
        file_path (Optional[str]): Custom file path to load from. If None, uses
                                 the default PROFILES_FILE path. Useful for testing
                                 and custom configurations.

    Returns:
        Dict[str, Dict[str, Any]]: Dictionary of user profiles keyed by username.
                                  Each profile contains user data including authentication,
                                  preferences, and activity history. Returns empty dict
                                  if file doesn't exist or contains invalid data.

    Raises:
        No exceptions are raised - all errors are logged and handled gracefully.

    Example:
        >>> profiles = load_profiles()
        >>> user_profile = profiles.get('john_doe')
        >>> if user_profile:
        ...     print(f"User: {user_profile['name']}")

    Note:
        - Thread-safe operation using file locks
        - Automatically adds missing 'histories' field for backward compatibility
        - Validates profile structure and logs warnings for invalid data
        - Returns empty dictionary on any error to prevent application crashes
    """
    with _file_lock:
        try:
            # Determine the file path to use
            path = file_path or PROFILES_FILE

            # Check if file exists and is readable
            if not os.path.exists(path):
                logging.warning(f"Profiles file not found: {path}")
                return {}

            if not os.access(path, os.R_OK):
                logging.error(f"No read permission for profiles file: {path}")
                return {}

            # Load and parse the JSON data
            with open(path, 'r', encoding='utf-8') as f:
                profiles_list = json.load(f)

            # Validate that we have a list of profiles
            if not isinstance(profiles_list, list):
                logging.error("Profiles file must contain a JSON array of profile objects")
                return {}

            # Convert list to dictionary and validate each profile
            profiles = {}
            valid_profiles_count = 0

            for i, profile in enumerate(profiles_list):
                # Ensure backward compatibility by adding missing fields
                profile.setdefault('histories', [])

                # Validate profile structure
                if not _validate_profile_structure(profile):
                    logging.warning(f"Skipping invalid profile at index {i}")
                    continue

                # Check for duplicate usernames
                username = profile['username']
                if username in profiles:
                    logging.warning(f"Duplicate username found: {username}. Using latest entry.")

                profiles[username] = profile
                valid_profiles_count += 1

            logging.info(f"Successfully loaded {valid_profiles_count} valid profiles from {path}")
            return profiles

        except FileNotFoundError:
            logging.error(f"Profiles file not found: {path}")
            return {}
        except json.JSONDecodeError as e:
            logging.error(f"Invalid JSON in profiles file {path}: {e}")
            return {}
        except PermissionError:
            logging.error(f"Permission denied accessing profiles file: {path}")
            return {}
        except Exception as e:
            logging.error(f"Unexpected error loading profiles from {path}: {e}")
            return {}


# --- Function: save_profiles ---
def save_profiles(profiles, file_path=None):
    """
    Save profiles to profiles.json.
    Args:
        profiles (dict): Profiles keyed by username.
    """
    with _file_lock:
        # Begin try block
        try:
            path = file_path or PROFILES_FILE
            with open(path, 'w') as f:
                json.dump(list(profiles.values()), f, indent=4)
            logging.info("Profiles saved successfully.")
        # Handle exception
        except Exception as e:
            logging.error(f"Error saving profiles.json: {e}")


# --- Function: update_profile ---
def update_profile(username, updated_data, file_path=None):
    """
    Update a specific user's profile.
    Args:
        username (str): The username.
        updated_data (dict): Data to update.
    """
    profiles = load_profiles(file_path)
    if username in profiles:
        profiles[username].update(updated_data)
        save_profiles(profiles, file_path)
    else:
        logging.warning(f"Attempted to update non-existent user '{username}'.")
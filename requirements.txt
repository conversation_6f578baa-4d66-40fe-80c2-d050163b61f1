alembic==1.14.0
annotated-types==0.7.0
anyio==4.6.2.post1
bleach==6.2.0
blinker==1.8.2
cachelib==0.9.0
cachetools==5.5.1
certifi==2024.8.30
charset-normalizer==3.4.1
click==8.1.7
colorama==0.4.6
commonmark==0.9.1
Deprecated==1.2.14
distro==1.9.0
Flask==3.0.3
Flask-Caching==2.3.0
Flask-Limiter==3.8.0
Flask-Login==0.6.3
Flask-Migrate==4.0.7
Flask-SQLAlchemy==3.1.1
Flask-WTF==1.2.2
google-ai-generativelanguage==0.6.15
google-api-core==2.24.1
google-api-python-client==2.160.0
google-auth==2.38.0
google-auth-httplib2==0.2.0
google-genai==1.1.0
google-generativeai==0.8.4
googleapis-common-protos==1.67.0rc1
greenlet==3.1.1
groq==0.12.0
grpcio==1.70.0
grpcio-status==1.70.0
h11==0.14.0
httpcore==1.0.7
httplib2==0.22.0
httpx==0.27.2
idna==3.10
importlib_resources==6.4.5
iniconfig==2.1.0
itsdangerous==2.2.0
Jinja2==3.1.4
jiter==0.8.2
joblib==1.4.2
limits==3.13.0
Mako==1.3.6
Markdown==3.7
markdown-it-py==3.0.0
MarkupSafe==3.0.2
mdurl==0.1.2
nltk==3.9.1
numpy==2.2.5
openai==1.61.1
ordered-set==4.1.0
packaging==24.1
pluggy==1.5.0
proto-plus==1.26.0
protobuf==5.29.3
psycopg2-binary==2.9.10
pyasn1==0.6.1
pyasn1_modules==0.4.1
pydantic==2.10.1
pydantic_core==2.27.1
Pygments==2.18.0
pyparsing==3.2.1
pytest==8.3.5
python-dotenv==1.0.1
regex==2024.11.6
requests==2.32.3
rich==13.9.4
rsa==4.9
scikit-learn==1.6.1
scipy==1.15.2
sniffio==1.3.1
SQLAlchemy==2.0.36
threadpoolctl==3.6.0
tqdm==4.67.0
typing_extensions==4.12.2
uritemplate==4.1.1
urllib3==2.3.0
webencodings==0.5.1
websockets==14.2
Werkzeug==3.1.1
wrapt==1.16.0
WTForms==3.2.1
